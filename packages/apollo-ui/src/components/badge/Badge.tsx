import { cva } from "class-variance-authority"
import classNames from "classnames"

import { Typography } from "../typography"
import styles from "./badge.module.css"
import { BadgeProps } from "./BadgeProps"

const badgeVariants = cva(styles.badgeRoot, {
  variants: {
    color: {
      default: styles.badgeDefault,
      process: styles.badgeProcess,
      success: styles.badgeSuccess,
      warning: styles.badgeWarning,
      error: styles.badgeError,
      disabled: styles.badgeDisabled,
    },
  },
  defaultVariants: {
    color: "default",
  },
})

export function BadgeStandalone({
  label,
  color = "default",
  icon,
  ref,
  className,
  ...divProps
}: BadgeProps) {
  return (
    <span
      {...divProps}
      ref={ref}
      className={classNames(
        "ApolloBadge-root",
        badgeVariants({
          color,
        }),
        className
      )}
    >
      {icon && (
        <span className={classNames(styles.badgeIcon, "ApolloBadge-icon")}>
          {icon}
        </span>
      )}
      <Typography level="labelLarge" className="ApolloBadge-label">
        {label}
      </Typography>
    </span>
  )
}

export function Badge({
  label,
  color = "default",
  icon,
  children,
  ref,
  wrapperRef,
  className,
  ...divProps
}: BadgeProps) {
  if (children) {
    return (
      <span ref={wrapperRef} className={classNames(styles.badgeWrapper, "ApolloBadge-wrapper", className)}>
        <BadgeStandalone
          label={label}
          color={color}
          icon={icon}
          ref={ref}
          className={styles.badgeIndicator}
          {...divProps}
        />
        {children}
      </span>
    )
  }
  return (
    <BadgeStandalone
      label={label}
      color={color}
      icon={icon}
      ref={ref}
      className={className}
      {...divProps}
    />
  )
}
