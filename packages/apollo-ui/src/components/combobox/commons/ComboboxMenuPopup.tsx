import {
  use<PERSON><PERSON>back,
  useMemo,
  useRef,
  type CSSProperties,
  type ReactNode,
  type RefObject,
} from "react"
import { Combobox as BaseCombobox } from "@base-ui-components/react/combobox"
import classNames from "classnames"

import { Portal } from "../../portal"
import { Typography } from "../../typography"
import styles from "../combobox.module.css"
import type {
  ComboboxGroup,
  ComboboxOption,
  ComboboxSlotProps,
  ComboboxValueType,
} from "../ComboboxProps"
import { ComboboxLoader } from "./ComboboxLoader"
import { ComboboxMenuItemRow } from "./ComboboxMenuItemRow"
import { ComboboxSelectAllItem } from "./ComboboxSelectAllItem"
import { ComboboxGroupLabel } from "./ComboboxGroupLabel"
import { isComboboxGroup } from "../utils/isComboboxGroup"

type ComboboxMenuPopupProps<
  ItemValue = string,
  Multiple extends boolean | undefined = false,
> = {
  containerRef: RefObject<HTMLDivElement | null>
  options?: (ComboboxOption | ComboboxGroup)[]
  filteredOptions?: string[] | { value: string; items: string[] }[] | ComboboxOption[]
  multiple?: boolean
  optionType?: "checkbox" | "text"
  showSelectAll?: boolean
  selectAllText?: string
  showGroupSelectAll?: boolean
  slotProps?: ComboboxSlotProps
  className?: string
  style?: CSSProperties
  noOptionsComponent?: ReactNode
  loading?: boolean
  loadingComponent?: ReactNode
  loadingMore?: boolean
  loadMoreLabel?: ReactNode
  hasMore?: boolean
  onLoadMore?: () => void
  onValueChange?: ((value: ComboboxValueType<ItemValue, Multiple> | null) => void)
}

export function ComboboxMenuPopup<
  ItemValue = string,
  Multiple extends boolean | undefined = false,
>({
  containerRef,
  options,
  filteredOptions,
  multiple,
  optionType = "text",
  showSelectAll = false,
  selectAllText = "Select All",
  showGroupSelectAll = false,
  slotProps,
  className,
  style,
  noOptionsComponent,
  loading = false,
  loadingComponent,
  loadingMore = false,
  loadMoreLabel,
  hasMore = false,
  onLoadMore,
  onValueChange
}: ComboboxMenuPopupProps<ItemValue, Multiple>) {
  
  const flatOptions = useMemo(() => {
    return (
      options?.flatMap((opt) => (isComboboxGroup(opt) ? opt.items : [opt])) ??
      []
    )
  }, [options])

  const getSelectedIds = useCallback((state: any): Set<string> => {
    if (!state?.selectedValues) return new Set()
    return new Set(state.selectedValues)
  }, [])

  const isGroupFullySelected = useCallback(
    (groupItemIds: string[], selectedIds: Set<string>): boolean => {
      return (
        groupItemIds.length > 0 &&
        groupItemIds.every((id) => selectedIds.has(id))
      )
    },
    []
  )

  const isGroupPartiallySelected = useCallback(
    (groupItemIds: string[], selectedIds: Set<string>): boolean => {
      return (
        groupItemIds.some((id) => selectedIds.has(id)) &&
        !groupItemIds.every((id) => selectedIds.has(id))
      )
    },
    []
  )

  const menuPopupObserver = useRef<IntersectionObserver | null>(null)
  const lastMenuElementRef = useCallback(
    (node: HTMLElement | null) => {
      if (loadingMore) return
      if (menuPopupObserver.current) menuPopupObserver.current.disconnect()
      menuPopupObserver.current = new IntersectionObserver((entries) => {
        if (entries[0].isIntersecting && hasMore) {
          onLoadMore?.()
        }
      })
      if (node) menuPopupObserver.current.observe(node)
    },
    [loadingMore, hasMore, onLoadMore]
  )
  return (
    <Portal baseComponent={<BaseCombobox.Portal />}>
      <BaseCombobox.Positioner
        positionMethod="fixed"
        sideOffset={8}
        anchor={containerRef}
        className={classNames(
          "ApolloCombobox-positioner",
          styles.combobox,
          styles.comboboxMenuPositioner,
          slotProps?.positioner?.className
        )}
      >
        <BaseCombobox.Popup
          className={classNames(
            "ApolloCombobox-popup",
            styles.comboboxMenuPopup,
            className,
            slotProps?.popup?.className
          )}
          style={style}
        >
          {loading ? (
            <ComboboxLoader
              className={slotProps?.loading?.className}
              label={loadingComponent}
              render={slotProps?.loading?.render}
            />
          ) : null}
          {multiple && showSelectAll && (filteredOptions && filteredOptions?.length > 0) && (
            <ComboboxSelectAllItem
              options={options}
              optionType={optionType}
              selectAllText={selectAllText}
            />
          )}
          <BaseCombobox.List
            className="ApolloCombobox-list"
            render={slotProps?.optionList?.render}
          >
            {slotProps?.optionList?.children ??
              ((item: ComboboxGroup | ComboboxOption | string) => {
                // Handle groups (item is a group object with value and items properties)
                if (
                  typeof item === "object" &&
                  item !== null &&
                  "items" in item &&
                  Array.isArray(item.items)
                ) {
                  const groupId = (item as any).value
                  const groupItemIds = (item as any).items as string[]

                  // Find the original group to get the label
                  const originalGroup = options?.find(
                    (opt) => isComboboxGroup(opt) && opt.id === groupId
                  ) as ComboboxGroup | undefined

                  if (!originalGroup) return null

                  return (
                    <BaseCombobox.Group
                      key={`group-${groupId}`}
                      items={groupItemIds}
                      ref={lastMenuElementRef}
                    >
                      <ComboboxGroupLabel
                        group={originalGroup}
                        groupItemIds={groupItemIds}
                        multiple={multiple}
                        optionType={optionType}
                        showGroupSelectAll={showGroupSelectAll}
                        getSelectedIds={getSelectedIds}
                        isGroupFullySelected={isGroupFullySelected}
                        isGroupPartiallySelected={isGroupPartiallySelected}
                        onClick={(e)=>{
                          e.preventDefault()
                          onValueChange?.(groupItemIds)
                        }}
                      />
                      <BaseCombobox.Collection>
                        {(collectionItemId) => {
                          const collectionItem = originalGroup.items.find(
                            (i) => i.id === collectionItemId
                          )
                          if (!collectionItem) return null

                          return (
                            <ComboboxMenuItemRow
                              key={`row-${collectionItem.id}`}
                              option={collectionItem}
                              multiple={multiple}
                              optionType={optionType}
                              slotProps={slotProps}
                              isSubRow={true}
                            />
                          )
                        }}
                      </BaseCombobox.Collection>
                    </BaseCombobox.Group>
                  )
                }

                // Handle regular items (item is a string ID)
                if (typeof item !== "string") return null

                const option = flatOptions.find(
                  (opt: ComboboxOption) => opt.id === item
                )
                if (!option) return null

                return (
                  <ComboboxMenuItemRow
                    key={`row-${option.id}`}
                    option={option}
                    multiple={multiple}
                    optionType={optionType}
                    slotProps={slotProps}
                    isSubRow={showSelectAll}
                    rowRef={lastMenuElementRef}
                  />
                )
              })}
          </BaseCombobox.List>
          {hasMore && loadingMore ? (
            <ComboboxLoader
              className={slotProps?.loadingMore?.className}
              label={loadMoreLabel ?? "Loading..."}
              render={slotProps?.loadingMore?.render}
            />
          ) : null}
          {!(loading || (hasMore && loadingMore)) && (
            <BaseCombobox.Empty
              className={classNames(
                "ApolloCombobox-noOptions",
                styles.comboboxNoOptions,
                slotProps?.noOptions?.className
              )}
              render={slotProps?.noOptions?.render}
            >
              {noOptionsComponent || (
                <Typography level="bodyMedium">No item</Typography>
              )}
            </BaseCombobox.Empty>
          )}
        </BaseCombobox.Popup>
      </BaseCombobox.Positioner>
    </Portal>
  )
}
